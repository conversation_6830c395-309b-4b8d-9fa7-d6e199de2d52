package pion.tech.pionbase.custom_view

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import pion.tech.pionbase.R
import kotlin.apply
import kotlin.let

class LEDView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // Paint objects for different layers
    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val xferPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    
    // Bitmaps and canvas for layers
    private var backgroundBitmap: Bitmap? = null
    private var textBitmap: Bitmap? = null
    private var xferBitmap: Bitmap? = null
    private var resultBitmap: Bitmap? = null
    private var backgroundCanvas: Canvas? = null
    private var textCanvas: Canvas? = null
    private var resultCanvas: Canvas? = null
    
    // Text properties
    private var displayText = "LED Banner View 🚀✨"
    private var textSize = 80f
    private var textColor = Color.WHITE
    private var backgroundColor = Color.BLACK
    
    // Animation properties
    private var textX = 0f
    private var textWidth = 0f
    private var animator: ValueAnimator? = null
    private var animationSpeed = 2000L // milliseconds for one cycle
    
    // Xfermode properties
    private var dotsBitmap: Bitmap? = null
    private val xferMode = PorterDuffXfermode(PorterDuff.Mode.DST_IN)

    // Dots pattern properties
    private var dotRadius = 8f
    private var dotSpacing = 6f
    
    init {
        setupPaints()
        loadXferBitmap()
        // Initialize text position
        textX = 0f
    }
    
    private fun setupPaints() {
        // Setup text paint
        textPaint.apply {
            color = textColor
            textSize = <EMAIL>
            typeface = Typeface.DEFAULT_BOLD
            isAntiAlias = true
        }
        
        // Setup background paint
        backgroundPaint.apply {
            color = backgroundColor
            style = Paint.Style.FILL
        }
        
        // Setup xfer paint
        xferPaint.apply {
            isAntiAlias = true
        }
    }

    fun createDotPatternBitmap(
        width: Int,
        height: Int,
        dotRadius: Float = 10f,
        spacing: Float = 20f,
        dotColor: Int = Color.TRANSPARENT,
        bgColor: Int = Color.BLACK
    ): Bitmap {
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)

        // Vẽ background đen
        canvas.drawColor(bgColor)

        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint.color = dotColor
        paint.style = Paint.Style.FILL

        // Tính toán khoảng cách giữa các chấm
        val stepX = dotRadius * 2 + spacing
        val stepY = dotRadius * 2 + spacing

        // Tính toán offset để căn giữa pattern
        val numDotsX = (width / stepX).toInt()
        val numDotsY = (height / stepY).toInt()
        val offsetX = (width - (numDotsX - 1) * stepX) / 2f
        val offsetY = (height - (numDotsY - 1) * stepY) / 2f

        // Vẽ các chấm tròn trong suốt theo pattern đều nhau
        for (row in 0 until numDotsY) {
            for (col in 0 until numDotsX) {
                val x = offsetX + col * stepX
                val y = offsetY + row * stepY
                canvas.drawCircle(x, y, dotRadius, paint)
            }
        }

        return bitmap
    }

    private fun loadXferBitmap() {
        // Không cần load từ file nữa, sẽ tạo động trong onSizeChanged
    }
    
    private fun startTextAnimation() {
        animator?.cancel()

        if (width > 0 && textWidth > 0) {
            animator = ValueAnimator.ofFloat(0f, 1f).apply {
                duration = animationSpeed
                repeatCount = ValueAnimator.INFINITE
                repeatMode = ValueAnimator.RESTART
                addUpdateListener { animation ->
                    val progress = animation.animatedValue as Float
                    // Start from right edge, move to left edge
                    textX = width.toFloat() - (textWidth + width) * progress
                    invalidate()
                }
            }
            animator?.start()
        }
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        if (w > 0 && h > 0) {
            // Create bitmaps for each layer
            backgroundBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
            textBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
            xferBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)

            backgroundCanvas = Canvas(backgroundBitmap!!)
            textCanvas = Canvas(textBitmap!!)

            // Tạo dots bitmap động với kích thước view
            dotsBitmap = createDotPatternBitmap(
                width = w,
                height = h,
                dotRadius = dotRadius,
                spacing = dotSpacing,
                dotColor = Color.TRANSPARENT,
                bgColor = Color.BLACK
            )

            // Calculate text width
            textWidth = textPaint.measureText(displayText)

            // Start animation
            startTextAnimation()
        }
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        val w = width
        val h = height
        
        if (w <= 0 || h <= 0 || backgroundBitmap == null || textBitmap == null) return
        
        // Clear all layers
        backgroundCanvas?.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)
        textCanvas?.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)
        
        // Layer 1: Draw background
        drawBackgroundLayer(backgroundCanvas!!, w, h)
        
        // Layer 2: Draw text
        drawTextLayer(textCanvas!!, w, h)
        
        // Layer 3: Apply xfermode effect
        drawXferLayer(canvas, w, h)
    }
    
    private fun drawBackgroundLayer(canvas: Canvas, width: Int, height: Int) {
        // Create gradient background
        val gradient = LinearGradient(
            0f, 0f, width.toFloat(), height.toFloat(),
            intArrayOf(
                Color.parseColor("#FF1744"),
                Color.parseColor("#FF5722"),
                Color.parseColor("#FF9800")
            ),
            null,
            Shader.TileMode.CLAMP
        )
        backgroundPaint.shader = gradient
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), backgroundPaint)
    }
    
    private fun drawTextLayer(canvas: Canvas, width: Int, height: Int) {
        // Calculate text position
        val textY = height / 2f + textSize / 3f
        
        // Draw text with current animation position
        canvas.drawText(displayText, textX, textY, textPaint)
    }
    
    private fun drawXferLayer(canvas: Canvas, width: Int, height: Int) {
        // Tạo bitmap tạm để áp dụng mask
        val tempBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val tempCanvas = Canvas(tempBitmap)

        // Vẽ background lên bitmap tạm
        tempCanvas.drawBitmap(backgroundBitmap!!, 0f, 0f, null)

        // Vẽ text lên bitmap tạm
        tempCanvas.drawBitmap(textBitmap!!, 0f, 0f, null)

        // Áp dụng mask với dots pattern
        dotsBitmap?.let { dots ->
            val maskPaint = Paint(Paint.ANTI_ALIAS_FLAG)
            maskPaint.xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_IN)

            // Vẽ mask lên bitmap tạm - chỉ những vùng trong suốt (dots) sẽ hiển thị
            tempCanvas.drawBitmap(dots, 0f, 0f, maskPaint)
        }

        // Vẽ kết quả cuối cùng lên canvas chính
        canvas.drawBitmap(tempBitmap, 0f, 0f, null)

        // Giải phóng bitmap tạm
        tempBitmap.recycle()
    }
    
    // Public methods to control the LED view
    fun setText(text: String) {
        displayText = text
        textWidth = textPaint.measureText(displayText)
        invalidate()
    }
    
    fun setTextColor(color: Int) {
        textColor = color
        textPaint.color = color
        invalidate()
    }
    
    fun setTextSize(size: Float) {
        textSize = size
        textPaint.textSize = size
        textWidth = textPaint.measureText(displayText)
        invalidate()
    }
    
    fun setAnimationSpeed(speedMs: Long) {
        animationSpeed = speedMs
        if (animator?.isRunning == true) {
            startTextAnimation()
        }
    }
    
    fun startAnimation() {
        startTextAnimation()
    }
    
    fun stopAnimation() {
        animator?.cancel()
    }

    // Phương thức để tùy chỉnh dots pattern
    fun setDotRadius(radius: Float) {
        dotRadius = radius
        // Tạo lại dots bitmap nếu view đã được khởi tạo
        if (width > 0 && height > 0) {
            dotsBitmap = createDotPatternBitmap(
                width = width,
                height = height,
                dotRadius = dotRadius,
                spacing = dotSpacing,
                dotColor = Color.TRANSPARENT,
                bgColor = Color.BLACK
            )
            invalidate()
        }
    }

    fun setDotSpacing(spacing: Float) {
        dotSpacing = spacing
        // Tạo lại dots bitmap nếu view đã được khởi tạo
        if (width > 0 && height > 0) {
            dotsBitmap = createDotPatternBitmap(
                width = width,
                height = height,
                dotRadius = dotRadius,
                spacing = dotSpacing,
                dotColor = Color.TRANSPARENT,
                bgColor = Color.BLACK
            )
            invalidate()
        }
    }

    fun setDotPattern(radius: Float, spacing: Float) {
        dotRadius = radius
        dotSpacing = spacing
        // Tạo lại dots bitmap nếu view đã được khởi tạo
        if (width > 0 && height > 0) {
            dotsBitmap = createDotPatternBitmap(
                width = width,
                height = height,
                dotRadius = dotRadius,
                spacing = dotSpacing,
                dotColor = Color.TRANSPARENT,
                bgColor = Color.BLACK
            )
            invalidate()
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        animator?.cancel()
    }
}
