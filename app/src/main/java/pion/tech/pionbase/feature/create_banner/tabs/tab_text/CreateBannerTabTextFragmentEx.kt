package pion.tech.pionbase.feature.create_banner.tabs.tab_text

import android.app.AlertDialog
import android.graphics.Color
import android.widget.ImageView
import android.widget.SeekBar
import androidx.core.content.ContextCompat
import pion.tech.pionbase.R
import pion.tech.pionbase.data.model.create_banner.TextStyleData
import pion.tech.pionbase.feature.create_banner.CreateBannerCommonViewModel
import pion.tech.pionbase.util.changeBackgroundColor
import pion.tech.pionbase.util.collectFlowOnView
import pion.tech.pionbase.util.setPreventDoubleClick
import pion.tech.pionbase.util.setTintColor
import androidx.core.graphics.toColorInt

fun CreateBannerTabTextFragment.setupTextSizeSeekBar() {
    binding.seekBarTextSize.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            if (fromUser) {
                val textSize = CreateBannerCommonViewModel.MIN_TEXT_SIZE +
                    (progress * (CreateBannerCommonViewModel.MAX_TEXT_SIZE - CreateBannerCommonViewModel.MIN_TEXT_SIZE)) / 100
                createBannerCommonViewModel.updateTextSize(textSize)
            }
        }

        override fun onStartTrackingTouch(seekBar: SeekBar?) {}
        override fun onStopTrackingTouch(seekBar: SeekBar?) {}
    })
}

fun CreateBannerTabTextFragment.setupOutlineSeekBar() {
    binding.seekBarOutline.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            if (fromUser) {
                createBannerCommonViewModel.updateOutlineWidth(progress)
            }
        }

        override fun onStartTrackingTouch(seekBar: SeekBar?) {}
        override fun onStopTrackingTouch(seekBar: SeekBar?) {}
    })
}

fun CreateBannerTabTextFragment.setupShadowSeekBar() {
    binding.seekBarShadow.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            if (fromUser) {
                createBannerCommonViewModel.updateShadowRadius(progress)
            }
        }

        override fun onStartTrackingTouch(seekBar: SeekBar?) {}
        override fun onStopTrackingTouch(seekBar: SeekBar?) {}
    })
}

fun CreateBannerTabTextFragment.showColorPickerDialog(
    currentColor: Int,
    onColorSelected: (Int) -> Unit
) {
    val colors = arrayOf(
        Color.RED, Color.GREEN, Color.BLUE, Color.YELLOW,
        Color.CYAN, Color.MAGENTA, Color.BLACK, Color.WHITE,
        Color.GRAY, Color.DKGRAY, Color.LTGRAY,
        ContextCompat.getColor(requireContext(), R.color.blue_1b71f4)
    )
    
    val colorNames = arrayOf(
        "Red", "Green", "Blue", "Yellow",
        "Cyan", "Magenta", "Black", "White",
        "Gray", "Dark Gray", "Light Gray", "Blue"
    )
    
    AlertDialog.Builder(requireContext())
        .setTitle("Select Color")
        .setItems(colorNames) { _, which ->
            onColorSelected(colors[which])
        }
        .setNegativeButton("Cancel", null)
        .show()
}


fun CreateBannerTabTextFragment.updateStyleTextImageState(
    imageView: ImageView,
    isSelected: Boolean
) {
    imageView.setTintColor(
        if (isSelected) {
            R.color.blue_1b71f4
        } else {
            R.color.gray_85888e
        }
    )
}

fun CreateBannerTabTextFragment.setUpTextColorPicker() {
    binding.viewTextColorPicker.setPreventDoubleClick {
        onTextColorPickerClick()
    }
}

fun CreateBannerTabTextFragment.setUpOutlineColorPicker() {
    binding.viewOutlineColorPicker.setPreventDoubleClick {
        onOutlineColorPickerClick()
    }
}

fun CreateBannerTabTextFragment.setUpShadowColorPicker() {
    binding.viewShadowColorPicker.setPreventDoubleClick {
        onShadowColorPickerClick()
    }
}

fun CreateBannerTabTextFragment.setupButtonClearFormat() {
    binding.btnClearFormat.setPreventDoubleClick {
        onClearFormatClick()
    }
}

fun CreateBannerTabTextFragment.setupButtonBold() {
    binding.btnBold.setPreventDoubleClick {
        onBoldClick()
    }
}

fun CreateBannerTabTextFragment.setupButtonItalic() {
    binding.btnItalic.setPreventDoubleClick {
        onItalicClick()
    }
}

fun CreateBannerTabTextFragment.setupButtonUnderline() {
    binding.btnUnderline.setPreventDoubleClick {
        onUnderlineClick()
    }
}

fun CreateBannerTabTextFragment.observeTextStyleData() {
    createBannerCommonViewModel.textStyleData.collectFlowOnView(viewLifecycleOwner) {
        updateUIFromTextStyleData(it)
    }
}

fun CreateBannerTabTextFragment.updateUIFromTextStyleData(data: TextStyleData) {
    // Update seekbars
    val textSizeProgress = ((data.textSize - CreateBannerCommonViewModel.MIN_TEXT_SIZE) * 100) /
            (CreateBannerCommonViewModel.MAX_TEXT_SIZE - CreateBannerCommonViewModel.MIN_TEXT_SIZE)
    binding.seekBarTextSize.progress = textSizeProgress
    binding.seekBarOutline.progress = data.outlineWidth
    binding.seekBarShadow.progress = data.shadowRadius

    // Update color pickers
    binding.viewTextColorPicker.setBackgroundColor(Color.parseColor(data.textColor))
    binding.viewOutlineColorPicker.setBackgroundColor(Color.parseColor(data.outlineColor))
    binding.viewShadowColorPicker.setBackgroundColor(Color.parseColor(data.shadowColor))

    // Update style buttons
    updateStyleTextImageState(binding.btnBold, data.isBold)
    updateStyleTextImageState(binding.btnItalic, data.isItalic)
    updateStyleTextImageState(binding.btnUnderline, data.isUnderline)
}

fun CreateBannerTabTextFragment.onTextColorPickerClick() {
    val currentColor = createBannerCommonViewModel.textStyleData.value.textColor
    showColorPickerDialog(currentColor.toColorInt()) { selectedColor ->
        createBannerCommonViewModel.updateTextColor(selectedColor.toString())
    }
}

fun CreateBannerTabTextFragment.onOutlineColorPickerClick() {
    val currentColor = createBannerCommonViewModel.textStyleData.value.outlineColor
    showColorPickerDialog(currentColor.toColorInt()) { selectedColor ->
        createBannerCommonViewModel.updateOutlineColor(selectedColor.toString())
    }
}

fun CreateBannerTabTextFragment.onShadowColorPickerClick() {
    val currentColor = createBannerCommonViewModel.textStyleData.value.shadowColor
    showColorPickerDialog(currentColor.toColorInt()) { selectedColor ->
        createBannerCommonViewModel.updateShadowColor(selectedColor.toString())
    }
}

fun CreateBannerTabTextFragment.onClearFormatClick() {
    createBannerCommonViewModel.clearTextFormat()
}

fun CreateBannerTabTextFragment.onBoldClick() {
    createBannerCommonViewModel.toggleTextBold()
}

fun CreateBannerTabTextFragment.onItalicClick() {
    createBannerCommonViewModel.toggleTextItalic()
}

fun CreateBannerTabTextFragment.onUnderlineClick() {
    createBannerCommonViewModel.toggleTextUnderline()
}