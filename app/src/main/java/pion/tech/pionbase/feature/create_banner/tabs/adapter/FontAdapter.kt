package pion.tech.pionbase.feature.create_banner.tabs.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.TextView
import pion.tech.pionbase.R
import pion.tech.pionbase.data.model.create_banner.FontItem

class FontAdapter(
    private val context: Context,
    private val fonts: List<FontItem>
) : BaseAdapter() {

    override fun getCount(): Int = fonts.size

    override fun getItem(position: Int): FontItem = fonts[position]

    override fun getItemId(position: Int): Long = position.toLong()

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val view = convertView ?: LayoutInflater.from(context).inflate(
            R.layout.item_font_spinner, parent, false
        )
        
        val textView = view.findViewById<TextView>(android.R.id.text1)
        textView.text = fonts[position].displayName
        
        return view
    }

    override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val view = convertView ?: LayoutInflater.from(context).inflate(
            R.layout.item_font_spinner, parent, false
        )
        
        val textView = view.findViewById<TextView>(android.R.id.text1)
        textView.text = fonts[position].displayName
        
        return view
    }
}