package pion.tech.pionbase.feature.create_banner

import android.graphics.Typeface
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.text.color
import androidx.fragment.app.Fragment
import dagger.hilt.android.AndroidEntryPoint
import pion.tech.pionbase.R
import pion.tech.pionbase.base.BaseFragment
import pion.tech.pionbase.data.model.create_banner.CreateBannerScreenTab
import pion.tech.pionbase.databinding.FragmentCreateBannerBinding
import pion.tech.pionbase.feature.create_banner.tabs.tab_text.CreateBannerTabTextFragment
import pion.tech.pionbase.util.collectFlowOnView

@AndroidEntryPoint
class CreateBannerFragment : BaseFragment<FragmentCreateBannerBinding, CreateBannerViewModel>(
    FragmentCreateBannerBinding::inflate,
    CreateBannerViewModel::class.java,
) {
    override fun init(view: View) {
        //TODO("Not yet implemented")
    }

    override fun subscribeObserver(view: View) {
        createBannerCommonViewModel.currentTab.collectFlowOnView(viewLifecycleOwner) { tab ->
            setUpUISelectedTab(tab)
            when(tab) {
                CreateBannerScreenTab.TEXT -> {
                    replaceFragment(CreateBannerTabTextFragment())
                }

                CreateBannerScreenTab.BACKGROUND -> {
                    //TODO("Not yet implemented")
                }

                CreateBannerScreenTab.EFFECT -> {
                    //TODO("Not yet implemented")
                }

                CreateBannerScreenTab.MUSIC -> {
                    //TODO("Not yet implemented")
                }
            }
        }
    }

    fun setUpUISelectedTab(tab: CreateBannerScreenTab) {
        resetTabStyles()

        when(tab) {
            CreateBannerScreenTab.TEXT -> {
                binding.tvTabText.setTextColor(ContextCompat.getColor(requireContext(), R.color.blue_1b71f4))
                binding.tvTabText.setBackgroundResource(R.drawable.bg_tab_selected)
                binding.tvTabText.typeface = Typeface.DEFAULT_BOLD
            }

            CreateBannerScreenTab.BACKGROUND -> {
                binding.tvBackground.setTextColor(ContextCompat.getColor(requireContext(), R.color.blue_1b71f4))
                binding.tvBackground.setBackgroundResource(R.drawable.bg_tab_selected)
                binding.tvBackground.typeface = Typeface.DEFAULT_BOLD
            }

            CreateBannerScreenTab.EFFECT -> {
                binding.tvTabEffect.setTextColor(ContextCompat.getColor(requireContext(), R.color.blue_1b71f4))
                binding.tvTabEffect.setBackgroundResource(R.drawable.bg_tab_selected)
                binding.tvTabEffect.typeface = Typeface.DEFAULT_BOLD
            }

            CreateBannerScreenTab.MUSIC -> {
                binding.tvTabMusic.setTextColor(ContextCompat.getColor(requireContext(), R.color.blue_1b71f4))
                binding.tvTabMusic.setBackgroundResource(R.drawable.bg_tab_selected)
                binding.tvTabMusic.typeface = Typeface.DEFAULT_BOLD
            }
        }
    }

    fun resetTabStyles() {
        binding.apply {
            val defaultColor = ContextCompat.getColor(requireContext(), R.color.text_secondary)

            tvTabText.setTextColor(defaultColor)
            tvTabText.setBackgroundColor(ContextCompat.getColor(requireContext(), android.R.color.transparent))
            tvTabText.typeface = Typeface.DEFAULT

            tvBackground.setTextColor(defaultColor)
            tvBackground.setBackgroundColor(ContextCompat.getColor(requireContext(), android.R.color.transparent))
            tvBackground.typeface = Typeface.DEFAULT

            tvTabEffect.setTextColor(defaultColor)
            tvTabEffect.setBackgroundColor(ContextCompat.getColor(requireContext(), android.R.color.transparent))
            tvTabEffect.typeface = Typeface.DEFAULT

            tvTabMusic.setTextColor(defaultColor)
            tvTabMusic.setBackgroundColor(ContextCompat.getColor(requireContext(), android.R.color.transparent))
            tvTabMusic.typeface = Typeface.DEFAULT
        }
    }

    fun replaceFragment(fragment: Fragment) {
        childFragmentManager.beginTransaction()
            .replace(R.id.fragmentContainer, fragment)
            .commit()
    }
}